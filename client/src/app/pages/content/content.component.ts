import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { ContentPurchaseModalComponent } from '@/components/content-purchase-modal/content-purchase-modal.component'
import { LinkPreviewComponent } from '@/components/link-preview/link-preview.component'
import { LoadingIndicatorComponent } from '@/components/loading-indicator/loading-indicator.component'
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component"
import { ClickOutsideDirective } from "@/directives/clickOutside"
import { environment } from "@/env/environment"
import { IContent } from "@/interfaces/content"

import { AuthService } from "@/services/auth.service"
import { ContentService } from "@/services/content.service"
import { ProfileService } from "@/services/profile.service"
import { SeoService } from "@/services/seo.service"
import { ShareDataService } from "@/services/share-data.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, DatePipe, isP<PERSON><PERSON><PERSON>rowser, isPlatformServer, NgOptimizedImage } from "@angular/common"
import { Component, DestroyRef, effect, HostListener, inject, PLATFORM_ID, signal } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { DomSanitizer, Meta, SafeResourceUrl, Title } from "@angular/platform-browser"
import { ActivatedRoute, Router } from "@angular/router"
import { TranslocoService } from "@jsverse/transloco"
import { TextInteractionComponent } from "./text-interaction/text-interaction.component"

@Component({
  selector: 'app-content',
  standalone: true,
  imports: [
    CommonModule,
    NgOptimizedImage,
    LinkPreviewComponent,
    DatePipe,
    BreadcrumbComponent,
    TextInteractionComponent,
    ClickOutsideDirective,
    FavoritesIconComponent,
    LoadingIndicatorComponent,
    ContentPurchaseModalComponent
],
  templateUrl: './content.component.html',
  styleUrl: './content.component.scss'
})
export class ContentComponent {
  translocoService = inject(TranslocoService)
  profileService = inject(ProfileService)
  contentService = inject(ContentService)
  shareDataService = inject(ShareDataService);
  sanitizer = inject(DomSanitizer)
  route = inject(ActivatedRoute)
  router = inject(Router)
  titleService = inject(Title)
  metaService = inject(Meta)
  auth = inject(AuthService)
  platformId = inject(PLATFORM_ID)
  content: IContent | null = null
  toasterService = inject(ToasterService);
  seoService = inject(SeoService);
  selection: any = signal(null)
  quote: string | null = null
  headers: any = []
  similar: any = []
  private readonly destroyRef = inject(DestroyRef);
  contents: boolean = false;
  currentPage = this.route.snapshot.params['page'];
  lang = 'ru';
  likesContent: any = [];
  favouriteContent: any = [];
  likesCount: number = 0
  previewImg: any = null;
  hrefRedirect = "";
  isLoading = false;
  quoteId = this.route.snapshot.queryParams['quoteId'];
  audioFileIndex = -1;
  imageLoadingStates: Record<string, boolean> = {};
  similarImageLoadingStates: Record<string, boolean> = {};
  showPurchaseModal = signal(false);
  contentData = signal<any>(null);
  remainingReadingTime = signal<string>('0 мин. чтения');

  // Cache for processed YouTube links to avoid recalculation
  private youtubeLinksCache = new Map<string, SafeResourceUrl>();
  private accessCache: boolean | null = null;
  private lastProfileCheck: any = null;

  get audio() {
    return [...this.content?.audio, ...this.content?.audioFiles]
  }

  get isMobile() {
    if (isPlatformServer(this.platformId)) {
      return false;
    }

    return window.innerWidth <= 768;
  }

  constructor() {
    effect(() => {

    });
  }
  ngOnInit() {
    
    // if(isPlatformServer(this.platformId)) {
    //   this.getContent(false);
    // }

    this.scrollToTop();

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(lang => {
      this.lang = lang;
      this.getContent()
    });
    this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      if (params['page']) {
        this.currentPage = params['page'];
        this.getContent(false);
      }
    })
  }

  moveLeft() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: -240,
        behavior: 'smooth'
      });
    }
  }

  moveRight() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: 240,
        behavior: 'smooth'
      });
    }
  }

  getContent(views = true) {
    this.hrefPreview = "";
    this.previewData = null;
    this.similar = [];
    this.similarCall = false;

    // Clear caches when loading new content
    this.youtubeLinksCache.clear();
    this.accessCache = null;
    this.lastProfileCheck = null;

    if (isPlatformBrowser(this.platformId)) {
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
    }
 

      this.contentService.getContent(this.lang, this.currentPage, views).subscribe({
        next: (res: any) => {
          if(res) {
            this.content = res;

            this.titleService.setTitle(res.seo_title || res.title);
            this.metaService.updateTag({ name: 'description', content: res.seo_description || res.description });

            if(isPlatformBrowser(this.platformId)) {
              // Enhanced SEO optimization
              const wordCount = this.seoService.countWords(res.content || '');
              const readingTime = this.seoService.calculateReadingTime(wordCount);

              // Initialize remaining reading time
              this.remainingReadingTime.set(`${readingTime} мин. чтения`);

              this.seoService.setSEOData({
                title: res.seo_title || res.title,
                description: res.seo_description || res.description,
                author: res.author || 'Advayta.org',
                publishedDate: res.created_at,
                modifiedDate: res.updated_at,
                image: res.preview?.name ? `${window.location.origin}/upload/${res.preview.name}` : undefined,
                url: window.location.href,
                type: 'article',
                wordCount,
                readingTime,
                keywords: res.tags?.map((tag: any) => tag.name).join(', ')
              });

              // Set structured data for better SEO
              this.seoService.setStructuredData({
                title: res.seo_title || res.title,
                description: res.seo_description || res.description,
                author: res.author || 'Advayta.org',
                publishedDate: res.created_at,
                modifiedDate: res.updated_at,
                image: res.preview?.name ? `${window.location.origin}/upload/${res.preview.name}` : undefined,
                url: window.location.href,
                type: 'article',
                wordCount,
                readingTime
              }, res.content);
            }

            if(this.quoteId) {
              this.contentService.getQuote(this.quoteId).subscribe((res: any) => {
                document.getElementById('content')!.innerHTML = document.getElementById('content')!.innerHTML.replace(res.quote.trim(), `<span class="quote">${res.quote.trim()}</span><style>.quote{background: var(--selection); color: black}</style>`)
                  const element: HTMLElement = document.querySelector('.quote')!;
                  if(element) {
                    window.scrollTo({
                      top: element.offsetTop + 500,
                      behavior: 'smooth'
                    });
                  }
              });
            }

            if(isPlatformBrowser(this.platformId)) {
              setTimeout(() => {
                this.setHeaders();
                this.isLoading = false;
              });
            }
          }
        },
        error: (err) => {
          console.error('Error loading content:', err);
          this.isLoading = false; // Make sure to reset loading state on error too
        }
      });
  }

  setHeaders() {
    this.headers = [];
      document.querySelectorAll('#content h1, #content h2, #content h3').forEach((el: Element) => {
        this.headers.push({
          tag: el.tagName,
          content: el.textContent
        })
        return el
      });
  }

  like(content: any) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.contentService.like(content.id).subscribe({
      next: (_r) => {
        if (content.liked) {
          content.likes--;
          this.toasterService.showToast('Статья удалена из понравившихся!', 'error', 'bottom-middle', 3000);
        } else {
          content.likes++;
          this.toasterService.showToast('Статья добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        }
        content.liked = !content.liked
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  favorites(content: any) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.contentService.addToFavourites(content.id).subscribe({
      next: (_r) => {
        if (content.inFavourites) {
          this.toasterService.showToast('Статья удалена из избранного!', 'error', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Статья добавлена в избранное!', 'success', 'bottom-middle', 3000);
        }
        content.inFavourites = !content.inFavourites;
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  scrollToTop(event?: MouseEvent): void {
    if (event && (event.ctrlKey || event.metaKey)) {
      return;
    }

    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  handleSimilarItemClick(event: MouseEvent, item: any): void {
    // Validate item data
    if (!item || !item.category || !item.category.id || !item.slug) {
      console.error('Invalid item data for navigation:', item);
      return;
    }

    // Handle Ctrl+Click, Cmd+Click (Mac), middle mouse button, or Shift+Click for new tab/window
    if (event.ctrlKey || event.metaKey || event.button === 1 || event.shiftKey) {
      event.preventDefault();
      event.stopPropagation();
      const url = `/${this.translocoService.getActiveLang()}/categories/${item.category.id}/${item.slug}`;
      window.open(url, '_blank');
      return;
    }

    // For regular clicks, prevent default and use Angular Router for SPA navigation
    event.preventDefault();
    event.stopPropagation();

    console.log('Regular click detected, navigating via Angular Router...');

    // Navigate using Angular Router for proper SPA behavior
    this.router.navigate([
      this.translocoService.getActiveLang(),
      'categories',
      item.category.id,
      item.slug
    ]).then((success) => {
      console.log('Navigation result:', success);
      if (success) {
        console.log('Navigation successful, scrolling to top...');
        // Scroll to top after successful navigation
        if (isPlatformBrowser(this.platformId)) {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      } else {
        console.log('Navigation was not successful');
      }
    }).catch(error => {
      console.error('Navigation failed:', error);
      // Fallback to window.location if router navigation fails
      if (isPlatformBrowser(this.platformId)) {
        window.location.href = `/${this.translocoService.getActiveLang()}/categories/${item.category.id}/${item.slug}`;
      }
    });
  }

  handleSimilarItemKeydown(event: KeyboardEvent, item: any): void {
    // Handle Enter and Space key presses for accessibility
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      // Create a synthetic MouseEvent for consistency
      const syntheticEvent = new MouseEvent('click', {
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        shiftKey: event.shiftKey,
        button: 0 // Left mouse button
      });
      this.handleSimilarItemClick(syntheticEvent, item);
    }
  }

  // @HostListener('click', ['$event'])
  // async handleClick(event: MouseEvent) {
  //   const target = event.target as HTMLAnchorElement;
  //   if (target.tagName === 'A' && target.href && !target.classList.contains('button-content')) {
  //       event.preventDefault();
  //       const url = new URL(target.href);
  //       const path = url.pathname;

  //       // Clear preview data before navigation
  //       this.previewData = null;
  //       // this.hrefPreview = "";

  //       await this.router.navigate([path]);
  //   }
  // }

  share(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/test/${content.slug}`).then(() =>
        this.toasterService.showToast('Ссылка на страницу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }

  previewData: any = null;
  previewPosition = { x: 0, y: 0 };

  hrefPreview: string = "";
  private previewTimeout: any;

  @HostListener('mouseover', ['$event'])
  async handleHover(event: MouseEvent) {

    if (isPlatformBrowser(this.platformId) && window.innerWidth <= 768) {
      return; // Exit early on mobile devices
    }

     this.showPagePreview(event)
  }

  showPagePreview(event: MouseEvent) {
    const target = event.target as HTMLAnchorElement;

    // First check if the hover event is within the #content element
    const contentElement = document.getElementById('content');
    if (!contentElement) return;

    // Check if the target is within the content element
    let isTargetInContent = false;
    let currentNode: Node | null = target;

    while (currentNode && currentNode !== document.body) {
      if (currentNode === contentElement) {
        isTargetInContent = true;
        break;
      }
      currentNode = currentNode.parentNode;
      if (!currentNode) break;
    }

    // If target is not within #content, do nothing
    if (!isTargetInContent) return;

    // Continue with the existing logic only if we're inside #content
    if (target.classList.contains('button-content')) {
      return;
    }

    if ((target.tagName === 'A' && target.href) && (this.hrefPreview !== target.href) &&
        target.target !== '_blank') {
      if ((event.target as HTMLElement).closest('app-link-preview')) return

      this.hrefPreview = target.href;

      const url = new URL(target.href);
      const path = url.pathname;


      const parts = path.split('/').filter(Boolean);
      const lang = parts[0];
      const slug = parts[parts.length - 1];

      // Store the original href value
      const originalHref = target.href;
      this.previewData = null;
      // Add routerLink attribute, data-angular-link attribute, and remove href
      if (!target.hasAttribute('routerLink')) {
        target.setAttribute('routerLink', path);
        target.setAttribute('data-angular-link', 'true');
        target.setAttribute('data-original-href', originalHref); // Store original href for reference
        // target.removeAttribute('href'); // Remove the href attribute
      }

      if (this.previewTimeout) {
        clearTimeout(this.previewTimeout);
      }

      this.previewTimeout = setTimeout(() => {
        this.contentService.getContentPreview(lang, slug)
          .subscribe((data: any) => {
            // Create a temporary DOM element to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = data.content;

            // Truncate the content while preserving HTML structure
            const truncatedHTML = this.truncateHTML(tempDiv.innerHTML, 200);

            this.previewData = truncatedHTML;
            this.previewImg = data.preview.name;
            this.hrefRedirect = `/${this.translocoService.getActiveLang()}/categories/${data.category.id}/${data.slug}`;

            // Calculate position relative to the link element, not mouse cursor
            this.previewPosition = this.calculatePreviewPosition(target);
          });
      }, 300);
    }
  }

  /**
   * Get zoom factor for coordinate adjustment
   */
  private getZoomFactor(): number {
    // Check if we're in a zoomed container
    const contentWrapper = document.querySelector('.content-height_wrap:not(.main-contetnt-wrapper)');
    if (contentWrapper) {
      return 0.8; // zoom: 0.8 from styles.scss
    }
    return 1;
  }

  /**
   * Calculate optimal position for link preview relative to the hovered link
   */
  private calculatePreviewPosition(linkElement: HTMLAnchorElement): { x: number; y: number } {
    if (!isPlatformBrowser(this.platformId)) {
      return { x: 0, y: 0 };
    }

    const linkRect = linkElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const scrollX = window.scrollX || document.documentElement.scrollLeft;
    const scrollY = window.scrollY || document.documentElement.scrollTop;

    // Get zoom factor to adjust coordinates
    const zoomFactor = this.getZoomFactor();

    // Preview dimensions (responsive based on viewport)
    const isMobile = viewportWidth <= 768;
    const previewWidth = isMobile ? Math.min(320, viewportWidth - 40) : 350;
    const previewHeight = isMobile ? 150 : 200;
    const margin = isMobile ? 5 : 10;

    // Calculate initial position below the link
    let x = linkRect.left + scrollX;
    let y = linkRect.bottom + scrollY + margin;

    // Horizontal positioning adjustments
    // Center the preview under the link if possible
    x = x + (linkRect.width / 2) - (previewWidth / 2);

    // Ensure preview doesn't go off the right edge
    if (x + previewWidth > viewportWidth + scrollX - 20) {
      x = viewportWidth + scrollX - previewWidth - 20;
    }

    // Ensure preview doesn't go off the left edge
    if (x < scrollX + 20) {
      x = scrollX + 20;
    }

    // Vertical positioning adjustments
    // If preview would go below viewport, position it above the link
    if (y + previewHeight > viewportHeight + scrollY - 20) {
      y = linkRect.top + scrollY - previewHeight - margin;

      // If positioning above would go above viewport, keep it below but adjust
      if (y < scrollY + 20) {
        y = linkRect.bottom + scrollY + margin;
        // Limit height if necessary
        if (y + previewHeight > viewportHeight + scrollY - 20) {
          y = Math.max(scrollY + 20, viewportHeight + scrollY - previewHeight - 20);
        }
      }
    }

    // Adjust final coordinates for zoom factor
    if (zoomFactor !== 1) {
      x = x / zoomFactor;
      y = y / zoomFactor;
    }

    return { x: Math.round(x), y: Math.round(y) };
  }



  @HostListener('click', ['$event'])
  handleLinkClick(event: MouseEvent) {
     const target = event.target as HTMLElement;
    const linkElement = target.tagName === 'A' ? target : target.closest('a');
    let url = null;

    if (linkElement) {
      event.preventDefault();
      event.stopPropagation();
      url = linkElement.getAttribute('href');
    }

    if (isPlatformBrowser(this.platformId) && window.innerWidth <= 768) {
      // На мобильном устройстве: если превью уже показано для этой ссылки, выполняем навигацию
      if (this.previewData && url && this.hrefPreview === url) {
        let link = target.getAttribute('routerLink');
        if(link) {
          this.hidePreview();
          this.router.navigateByUrl(link);
        } else if(url) {
          this.hidePreview();
          window.open(url, '_blank');
        }
        return;
      }
      // Иначе показываем превью
      this.showPagePreview(event);
      return;
    }

    let link = target.getAttribute('routerLink');

    if(link)
      this.router.navigateByUrl(link);
    else if(url) {
      window.open(url, '_blank')
    }
  }

  @HostListener('mouseout', ['$event'])
  handleMouseOut(event: MouseEvent) {
    if (!(event.relatedTarget as HTMLElement)?.closest('app-link-preview') &&
      !((event.target as HTMLElement)?.tagName === 'A')) {
      this.hrefPreview = "";
      this.previewData = null;
    }
  }

  hidePreview() {
    this.hrefPreview = "";
    this.previewData = null;
    if (this.previewTimeout) {
      clearTimeout(this.previewTimeout);
    }
  }

  goToCategory(tagId: number) {
    this.router.navigate([`/${this.translocoService.getActiveLang()}/categories/${this.content!.category.id}`], { queryParams: { tags: tagId } })
  }

  toHeader(item: any) {
    const headers = document.querySelectorAll('h1, h2, h3');
    const filtered = Array.from(headers).find((el: Element) => el.textContent === item.content);
    if (filtered) {
      const elementPosition = filtered.getBoundingClientRect().top + window.pageYOffset;
      // Adjust offset based on screen size to account for sticky header and actions bar
      const isMobile = window.innerWidth <= 1100;
      const headerOffset = isMobile ? 79 : 120; // Mobile: 59px header + 20px, Desktop: 78px header + 20px
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      });
    }
  }

  createEmbedLink(youtubeUrl: string | undefined): string {
    if (!youtubeUrl) return "";
    // Handle both youtube.com and youtu.be formats
    const patterns = {
      long: /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
      short: /youtu\.be\/([^"&?\/\s]{11})/
    };

    const match = youtubeUrl.match(patterns.long) || youtubeUrl.match(patterns.short);

    if (match && match[1]) {
      const videoId = match[1];
      return `https://www.youtube.com/embed/${videoId}`;
    }

    return youtubeUrl; // Return original URL if no match found instead of throwing error
  }

  getSanitizedVideoUrl(youtubeUrl: string | undefined): SafeResourceUrl {
    if (!youtubeUrl) return this.sanitizer.bypassSecurityTrustResourceUrl("");

    // Check cache first
    if (this.youtubeLinksCache.has(youtubeUrl)) {
      return this.youtubeLinksCache.get(youtubeUrl)!;
    }

    // Create and cache the sanitized URL
    const embedUrl = this.createEmbedLink(youtubeUrl);
    const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(embedUrl);
    this.youtubeLinksCache.set(youtubeUrl, sanitizedUrl);

    return sanitizedUrl;
  }

  sanitizeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  isLiked(id: number) {
    return this.likesContent.includes(id)
  }

  isInFavourites(id: number) {
    return this.favouriteContent.includes(id)
  }

  protected readonly environment = environment;

  truncateHTML(html: string, maxLength: number): string {
    if (!html) return '';

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    let textContent = tempDiv.textContent || '';
    if (textContent.length <= maxLength) {
      return html;
    }

    let truncated = '';
    let currentLength = 0;
    let openTags: string[] = [];

    // Simple HTML parser
    let inTag = false;
    let currentTag = '';
    let currentText = '';

    for (let i = 0; i < html.length; i++) {
      const char = html[i];

      if (char === '<') {
        // Add accumulated text if we're not in a tag
        if (!inTag && currentText) {
          if (currentLength + currentText.length > maxLength) {
            // Truncate text
            truncated += currentText.substring(0, maxLength - currentLength) + '...';
            currentLength = maxLength;
            break;
          } else {
            truncated += currentText;
            currentLength += currentText.length;
            currentText = '';
          }
        }

        inTag = true;
        currentTag = '<';
      } else if (char === '>' && inTag) {
        currentTag += '>';
        inTag = false;

        // Check if it's a closing tag
        if (currentTag.indexOf('</') === 0) {
          if (openTags.length > 0) {
            openTags.pop();
          }
        } else if (currentTag.indexOf('/>') === -1 &&
                  !currentTag.match(/<(br|hr|img|input|link|meta|area|base|col|embed|keygen|param|source|track|wbr)/i)) {
          // Extract tag name
          const tagMatch = currentTag.match(/<([a-z0-9]+)/i);
          if (tagMatch && tagMatch[1]) {
            openTags.push(tagMatch[1]);
          }
        }

        truncated += currentTag;
        currentTag = '';

        // If we've reached the max length and processed a tag, check if we should exit
        if (currentLength >= maxLength && !inTag) {
          break;
        }
      } else if (inTag) {
        currentTag += char;
      } else {
        currentText += char;
      }
    }

    // Close any open tags
    for (let i = openTags.length - 1; i >= 0; i--) {
      truncated += `</${openTags[i]}>`;
    }

    return truncated;
  }

  showScrollTop: boolean = false;
  similarCall: boolean = false;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (isPlatformBrowser(this.platformId)) {
      this.showScrollTop = window.scrollY > 500;

      // Calculate remaining reading time based on scroll position
      this.updateRemainingReadingTime();

      if(this.showScrollTop){
          if(this.similar.length === 0 && this.content?.tags.length > 0 && !this.similarCall) {
            this.similarCall = true;
            this.contentService.getSimilar(this.route.snapshot.params['page']).subscribe((res: any) => this.similar = res)
          }
      }
    }
  }

  copyToClipboard(text: string, type: string): void {
    if (isPlatformBrowser(this.platformId)) {
      navigator.clipboard.writeText(text)
        .then(() => {
          this.toasterService.showToast(
            `${type} скопирован в буфер обмена!`,
            'success',
            'bottom-middle',
            3000
          );
        })
        .catch(err => {
          console.error('Не удалось скопировать текст: ', err);
          this.toasterService.showToast(
            'Не удалось скопировать текст',
            'error',
            'bottom-middle',
            3000
          );
        });
    }
  }

  updateRemainingReadingTime(): void {
    if (!this.content?.content || !isPlatformBrowser(this.platformId)) {
      return;
    }

    const contentElement = document.getElementById('content');
    if (!contentElement) {
      return;
    }

    try {
      // Get scroll position and content dimensions with better mobile support
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
      const windowHeight = window.innerHeight || document.documentElement.clientHeight;

      // Use getBoundingClientRect for better cross-platform compatibility
      const contentRect = contentElement.getBoundingClientRect();
      const contentTop = contentRect.top + scrollTop;
      const contentHeight = contentRect.height;

      // Calculate document height for better accuracy
      const documentHeight = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.clientHeight,
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      );

      // More accurate reading progress calculation
      let readingProgress = 0;

      // Check if user has scrolled past the content start
      if (scrollTop + windowHeight > contentTop) {
        // Calculate how much of the content area has been scrolled through
        const scrolledPastContentStart = Math.max(0, scrollTop - contentTop);
        const visibleContentHeight = Math.min(contentHeight, scrolledPastContentStart + windowHeight);

        // Calculate progress based on how much content has been scrolled through
        readingProgress = Math.min(1, visibleContentHeight / contentHeight);

        // If user is near the bottom of the page, consider it fully read
        const distanceFromBottom = documentHeight - (scrollTop + windowHeight);
        if (distanceFromBottom < 100) { // Within 100px of bottom
          readingProgress = 1;
        }
      }

      // Calculate remaining content
      const remainingProgress = Math.max(0, 1 - readingProgress);

      // Get total reading time and calculate remaining
      const totalWords = this.content.content.trim().split(/\s+/).length;
      const wordsPerMinute = 225;
      const totalMinutes = Math.ceil(totalWords / wordsPerMinute);
      const remainingMinutes = Math.ceil(totalMinutes * remainingProgress);

      // Hide reading time if article is fully read or remaining time is 0
      if (remainingMinutes <= 0 || readingProgress >= 0.95) {
        this.remainingReadingTime.set('');
      } else {
        this.remainingReadingTime.set(`${remainingMinutes} мин. чтения`);
      }
    } catch (error) {
      // Fallback for any calculation errors
      console.warn('Error calculating reading time:', error);
      // Keep the original reading time if calculation fails
    }
  }

  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/images/meadow.avif';
  }

  secondsToHMS(seconds: any) {
    const numSeconds = Number(seconds);

    // Проверяем на валидность числа
    if (isNaN(numSeconds) || numSeconds < 0) {
      return '00:00:00';
    }

    const hours = Math.floor(numSeconds / 3600);
    const minutes = Math.floor((numSeconds % 3600) / 60);
    const secs = Math.floor(numSeconds % 60);

    const pad = (num: number) => num.toString().padStart(2, '0');

    return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
  }

  play(audio: any) {
    if(audio.paid && this.content && (this.content.priceRub || this.content.priceEur) && !this.content.isPurchased) {
      this.toasterService.showToast('Платный материал, необходимо купить курс для прослушивания аудио', 'error', 'bottom-middle', 3000);
      return;
    }

    if(!this.content?.isPurchased && audio.paid && !this.hasAccess()) {
      this.toasterService.showToast('Платный материал, оформите подписку', 'error', 'bottom-middle', 3000)
      return;
    }

    audio.link = audio?.link || audio.url;
    this.shareDataService.changePlaylist([audio])
  }

  hasAccess() {
    // Check if profile has changed since last check
    if (this.lastProfileCheck === this.profileService.profile && this.accessCache !== null) {
      return this.accessCache;
    }

    // Update cache
    this.lastProfileCheck = this.profileService.profile;

    if (!this.profileService.profile) {
      this.accessCache = false;
      return false;
    }

    this.accessCache = this.profileService.profile.subscriptions.some((e: any) =>
      ['FULL_ACCESS', 'LIBRARY_AND_COURSES', 'COURSES', 'AUDIO_AND_COURSES'].includes(e.type)
    );

    return this.accessCache;
  }

  onMainImageLoad() {
    setTimeout(() => {
      this.imageLoadingStates['main'] = false;
    }, 300);
  }

  onMainImageError() {
    this.imageLoadingStates['main'] = false;
  }

  onSimilarImageLoad(itemId: string) {
    setTimeout(() => {
      this.similarImageLoadingStates[itemId] = false;
    }, 300);
  }

  onSimilarImageError(itemId: string) {
    this.similarImageLoadingStates[itemId] = false;
  }

  openPurchaseModal() {
    if(!this.profileService.profile) {
      this.toasterService.showToast('Необходимо авторизоваться', 'error', 'bottom-middle');
      return;
    }
    this.contentData.set(this.content);
    this.showPurchaseModal.set(true);
  }

  closePurchaseModal() {
    this.showPurchaseModal.set(false);
  }

  onPurchaseComplete() {
    this.showPurchaseModal.set(false);
    this.getContent(false);
  }

  shouldShowPurchaseButton(): boolean {
    if (!this.content) return false;

    if (this.content.isPurchased) return false;

    return !!(this.content.priceEur || this.content.priceRub);
  }

  hasContentAccess(): boolean {
    if (!this.content) return false;

    if (this.content.isPurchased) return true;

    if (this.content.priceEur || this.content.priceRub) return false;

    if (this.content.paid && this.profileService.profile) {
      return this.profileService.profile.subscriptions?.some((sub: any) =>
        ['COURSES', 'AUDIO_AND_COURSES', 'LIBRARY_AND_COURSES', 'FULL_ACCESS'].includes(sub.type)
      );
    }

    return !this.content.paid;
  }
}

